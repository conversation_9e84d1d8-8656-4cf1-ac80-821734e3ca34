import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from './contexts/AuthContext';
import { ROUTES } from './utils/constants';

// Pages
import HomePage from './pages/customer/HomePage';
import LoginPage from './pages/customer/LoginPage';
import RegisterPage from './pages/customer/RegisterPage';
import MenuPage from './pages/customer/MenuPage';
import AboutPage from './pages/customer/AboutPage';
import ProfilePage from './pages/customer/ProfilePage';
import ComingSoonPage from './pages/ComingSoonPage';

// Test Components
import { GoogleMapsTest } from './components/test/GoogleMapsTest';

// Admin Pages
import DashboardPage from './pages/admin/DashboardPage';
import ProductsPage from './pages/admin/ProductsPage';

// Layout Components
import { AdminRoute, CustomerRoute } from './components/layout/ProtectedRoute';
import MainLayout from './components/layout/MainLayout';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <MainLayout>
            <Routes>
              {/* Public Routes */}
              <Route path={ROUTES.HOME} element={<HomePage />} />
              <Route path={ROUTES.LOGIN} element={<LoginPage />} />
              <Route path={ROUTES.REGISTER} element={<RegisterPage />} />
              <Route path={ROUTES.MENU} element={<MenuPage />} />
              <Route path={ROUTES.ABOUT} element={<AboutPage />} />

              {/* Test Routes */}
              <Route path="/test/maps" element={<GoogleMapsTest />} />

            {/* Customer Routes */}
            <Route
              path={ROUTES.CUSTOMER.PROFILE}
              element={
                <CustomerRoute>
                  <ProfilePage />
                </CustomerRoute>
              }
            />

            {/* Admin Routes */}
            <Route
              path={ROUTES.ADMIN.DASHBOARD}
              element={
                <AdminRoute>
                  <DashboardPage />
                </AdminRoute>
              }
            />
            <Route
              path={ROUTES.ADMIN.MENU}
              element={
                <AdminRoute>
                  <ProductsPage />
                </AdminRoute>
              }
            />

              {/* Coming Soon Page */}
              <Route path="*" element={<ComingSoonPage />} />
            </Routes>
          </MainLayout>
        </Router>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
