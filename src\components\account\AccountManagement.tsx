import { useState } from 'react';
import { Tab } from '@headlessui/react';
import { 
  UserIcon, 
  MapPinIcon, 
  ShoppingBagIcon,
  ArrowRightOnRectangleIcon 
} from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import { ProfileTab } from './ProfileTab';
import { AddressTab } from './AddressTab';
import { OrdersTab } from './OrdersTab';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

interface AccountManagementProps {
  defaultTab?: number;
  className?: string;
}

export function AccountManagement({ defaultTab = 0, className = '' }: AccountManagementProps) {
  const { user, logout } = useAuth();
  const [selectedIndex, setSelectedIndex] = useState(defaultTab);

  const tabs = [
    {
      name: 'Thông tin cá nhân',
      icon: UserIcon,
      component: ProfileTab,
    },
    {
      name: '<PERSON><PERSON><PERSON> chỉ',
      icon: MapPinIcon,
      component: AddressTab,
    },
    {
      name: '<PERSON>ơ<PERSON> hàng',
      icon: ShoppingBagIcon,
      component: OrdersTab,
    },
  ];

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (!user) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Đang tải thông tin tài khoản...</p>
      </div>
    );
  }

  // Debug: Log user data
  console.log('AccountManagement - User data:', user);
  console.log('AccountManagement - User roles:', user.roles);

  return (
    <div className={`w-full max-w-6xl mx-auto ${className}`}>
      {/* Header */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 bg-orange-100 rounded-full flex items-center justify-center">
                <span className="text-xl font-semibold text-orange-600">
                  {user.fullName.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{user.fullName}</h1>
                <p className="text-gray-600">@{user.username}</p>
                <p className="text-sm text-gray-500">{user.email}</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
            >
              <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
              Đăng xuất
            </button>
          </div>
        </div>

        {/* User Stats */}
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {user.roles.length}
              </div>
              <div className="text-sm text-gray-500">Vai trò</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">0</div>
              <div className="text-sm text-gray-500">Đơn hàng</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {new Date(user.createdAt).getFullYear()}
              </div>
              <div className="text-sm text-gray-500">Năm tham gia</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <Tab.Group selectedIndex={selectedIndex} onChange={setSelectedIndex}>
          <Tab.List className="flex space-x-1 rounded-t-lg bg-gray-50 p-1">
            {tabs.map((tab, index) => (
              <Tab
                key={tab.name}
                className={({ selected }) =>
                  classNames(
                    'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                    'ring-white ring-opacity-60 ring-offset-2 ring-offset-orange-400 focus:outline-none focus:ring-2',
                    selected
                      ? 'bg-white text-orange-700 shadow'
                      : 'text-gray-600 hover:bg-white/[0.12] hover:text-gray-800'
                  )
                }
              >
                <div className="flex items-center justify-center space-x-2">
                  <tab.icon className="h-5 w-5" />
                  <span>{tab.name}</span>
                </div>
              </Tab>
            ))}
          </Tab.List>
          
          <Tab.Panels className="mt-2">
            {tabs.map((tab, index) => (
              <Tab.Panel
                key={index}
                className={classNames(
                  'rounded-b-lg bg-white p-6',
                  'ring-white ring-opacity-60 ring-offset-2 ring-offset-orange-400 focus:outline-none focus:ring-2'
                )}
              >
                <tab.component />
              </Tab.Panel>
            ))}
          </Tab.Panels>
        </Tab.Group>
      </div>
    </div>
  );
}

export default AccountManagement;
