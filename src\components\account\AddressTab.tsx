import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  MapPinIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { GoogleMap } from '../maps/GoogleMap';
import { addressService, type Address as ApiAddress } from '../../services/address.service';
import type { MapLocation } from '../../hooks/useGoogleMaps';

// Google Maps API Key
const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || 'AIzaSyCFTgWDocizT3d4PZYqj_gPNfbnEA39HNw';

// Address validation schema
const addressSchema = z.object({
  label: z.string().min(1, 'Nhãn địa chỉ không được để trống'),
  fullAddress: z.string().min(5, 'Địa chỉ phải có ít nhất 5 ký tự'),
});

type AddressFormData = z.infer<typeof addressSchema>;

export function AddressTab() {
  const [addresses, setAddresses] = useState<ApiAddress[]>([]);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
  } = useForm<AddressFormData>({
    resolver: zodResolver(addressSchema),
  });

  // Load addresses when component mounts
  useEffect(() => {
    loadAddresses();
  }, []);

  const loadAddresses = async () => {
    setIsLoading(true);
    try {
      const userAddresses = await addressService.getAddresses();
      setAddresses(userAddresses);
    } catch (error: any) {
      console.error('Error loading addresses:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLocationSelect = (location: MapLocation) => {
    setSelectedLocation(location);
    if (location.address) {
      setValue('fullAddress', location.address);
    }
  };

  const onSubmit = async (data: AddressFormData) => {
    if (!selectedLocation) {
      alert('Vui lòng chọn một địa điểm trên bản đồ');
      return;
    }

    try {
      if (editingId) {
        // Update existing address
        const updatedAddress = await addressService.updateAddress(editingId, {
          label: data.label,
          fullAddress: data.fullAddress,
          latitude: selectedLocation.lat,
          longitude: selectedLocation.lng,
        });

        setAddresses(prev =>
          prev.map(addr => addr.id === editingId ? updatedAddress : addr)
        );
        setEditingId(null);
      } else {
        // Add new address
        const newAddress = await addressService.createAddress({
          label: data.label,
          fullAddress: data.fullAddress,
          latitude: selectedLocation.lat,
          longitude: selectedLocation.lng,
        });

        setAddresses(prev => [...prev, newAddress]);
        setIsAddingNew(false);
      }

      reset();
      setSelectedLocation(null);
    } catch (error: any) {
      console.error('Error saving address:', error);
      alert(error.message || 'Có lỗi xảy ra khi lưu địa chỉ');
    }
  };

  const handleEdit = (address: ApiAddress) => {
    setEditingId(address.id!);
    setIsAddingNew(false);
    reset({
      label: address.label,
      fullAddress: address.fullAddress,
    });
    setSelectedLocation({
      lat: address.latitude,
      lng: address.longitude,
      address: address.fullAddress,
    });
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Bạn có chắc chắn muốn xóa địa chỉ này?')) {
      return;
    }

    try {
      await addressService.deleteAddress(id);
      setAddresses(prev => prev.filter(addr => addr.id !== id));
    } catch (error: any) {
      console.error('Error deleting address:', error);
      alert(error.message || 'Có lỗi xảy ra khi xóa địa chỉ');
    }
  };

  const handleCancel = () => {
    setIsAddingNew(false);
    setEditingId(null);
    setSelectedLocation(null);
    reset();
  };

  const handleAddNew = () => {
    setIsAddingNew(true);
    setEditingId(null);
    setSelectedLocation(null);
    reset({
      label: '',
      fullAddress: '',
    });
  };

  const openGoogleMaps = (latitude: number, longitude: number) => {
    const url = `https://www.google.com/maps?q=${latitude},${longitude}`;
    window.open(url, '_blank');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Địa chỉ giao hàng</h3>
        {!isAddingNew && !editingId && (
          <button
            onClick={handleAddNew}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Thêm địa chỉ mới
          </button>
        )}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="border rounded-lg p-4 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      )}

      {/* Address List */}
      {!isLoading && (
        <div className="space-y-4">
          {addresses.map((address) => (
            <div
              key={address.id}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <MapPinIcon className="h-5 w-5 text-orange-500 flex-shrink-0" />
                    <h4 className="font-medium text-gray-900">{address.label}</h4>
                  </div>
                  <p className="text-sm text-gray-900 mb-2 line-clamp-2">
                    {address.fullAddress}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                    <span>Lat: {address.latitude.toFixed(4)}</span>
                    <span>Lng: {address.longitude.toFixed(4)}</span>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => openGoogleMaps(address.latitude, address.longitude)}
                      className="text-xs bg-orange-600 text-white px-2 py-1 rounded hover:bg-orange-700 transition-colors"
                    >
                      Xem trên bản đồ
                    </button>
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => handleEdit(address)}
                    className="text-gray-400 hover:text-gray-600"
                    title="Chỉnh sửa"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(address.id!)}
                    className="text-gray-400 hover:text-red-600"
                    title="Xóa"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && addresses.length === 0 && !isAddingNew && (
        <div className="text-center py-12">
          <MapPinIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Chưa có địa chỉ nào
          </h3>
          <p className="text-gray-500 mb-6">
            Thêm địa chỉ đầu tiên để quản lý giao hàng
          </p>
          <button
            onClick={handleAddNew}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Thêm địa chỉ đầu tiên
          </button>
        </div>
      )}

      {/* Add/Edit Form */}
      {(isAddingNew || editingId) && (
        <div className="border border-gray-200 rounded-lg p-6 bg-gray-50">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900">
              {editingId ? 'Chỉnh sửa địa chỉ' : 'Thêm địa chỉ mới'}
            </h4>
            <div className="flex space-x-2">
              <button
                onClick={handleSubmit(onSubmit)}
                disabled={isSubmitting}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
              >
                <CheckIcon className="h-4 w-4 mr-2" />
                {isSubmitting ? 'Đang lưu...' : 'Lưu'}
              </button>
              <button
                onClick={handleCancel}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                <XMarkIcon className="h-4 w-4 mr-2" />
                Hủy
              </button>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Google Map */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chọn vị trí trên bản đồ
              </label>
              <GoogleMap
                center={selectedLocation || undefined}
                onLocationSelect={handleLocationSelect}
                height="h-64"
                showInstructions={false}
                apiKey={GOOGLE_MAPS_API_KEY}
              />
            </div>

            {/* Address Label */}
            <div>
              <label htmlFor="label" className="block text-sm font-medium text-gray-700">
                Nhãn địa chỉ
              </label>
              <input
                {...register('label')}
                type="text"
                id="label"
                placeholder="Ví dụ: Nhà riêng, Văn phòng, Nhà bạn..."
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              />
              {errors.label && (
                <p className="mt-1 text-sm text-red-600">{errors.label.message}</p>
              )}
            </div>

            {/* Full Address */}
            <div>
              <label htmlFor="fullAddress" className="block text-sm font-medium text-gray-700">
                Địa chỉ đầy đủ
              </label>
              <textarea
                {...register('fullAddress')}
                id="fullAddress"
                rows={3}
                placeholder="Địa chỉ sẽ được tự động điền khi bạn chọn trên bản đồ"
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              />
              {errors.fullAddress && (
                <p className="mt-1 text-sm text-red-600">{errors.fullAddress.message}</p>
              )}
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
