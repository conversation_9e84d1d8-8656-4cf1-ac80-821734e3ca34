import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { PencilIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import { VALIDATION } from '../../utils/constants';

// Validation schema
const profileSchema = z.object({
  username: z.string().min(1, 'Username không được để trống'),
  fullName: z
    .string()
    .min(VALIDATION.NAME_MIN_LENGTH, `Họ tên phải có ít nhất ${VALIDATION.NAME_MIN_LENGTH} ký tự`)
    .max(VALIDATION.NAME_MAX_LENGTH, `<PERSON>ọ tên không được quá ${VALIDATION.NAME_MAX_LENGTH} ký tự`),
  email: z
    .string()
    .email('Vui lòng nhập địa chỉ email hợp lệ'),
  phoneNumber: z
    .string()
    .regex(VALIDATION.PHONE_REGEX, 'Vui lòng nhập số điện thoại hợp lệ')
    .min(10, 'Số điện thoại phải có ít nhất 10 số'),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export function ProfileTab() {
  const [isEditing, setIsEditing] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const { user, updateProfile, isLoading, error, clearError } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isDirty },
    reset,
    setError,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
  });

  // Reset form when user data changes
  useEffect(() => {
    if (user) {
      reset({
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        phoneNumber: user.phoneNumber,
      });
    }
  }, [user, reset]);

  const onSubmit = async (data: ProfileFormData) => {
    try {
      clearError();
      // Send the fields that can be updated
      // Note: username is automatically included by the auth service
      const updateData = {
        fullName: data.fullName,
        email: data.email,
        phoneNumber: data.phoneNumber,
      };

      console.log('ProfileTab - Submitting update data:', updateData);
      await updateProfile(updateData);
      setIsEditing(false);
      setShowSuccessMessage(true);
      setTimeout(() => setShowSuccessMessage(false), 3000);
    } catch (error: any) {
      // Handle specific validation errors
      if (error.message.toLowerCase().includes('email')) {
        setError('email', { message: error.message });
      } else if (error.message.toLowerCase().includes('phone')) {
        setError('phoneNumber', { message: error.message });
      } else if (error.message.toLowerCase().includes('username')) {
        setError('username', { message: error.message });
      } else {
        setError('root', { message: error.message });
      }
    }
  };

  const handleCancel = () => {
    if (user) {
      reset({
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        phoneNumber: user.phoneNumber,
      });
    }
    setIsEditing(false);
    clearError();
  };

  if (!user) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Đang tải thông tin...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Success Message */}
      {showSuccessMessage && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <CheckIcon className="h-5 w-5 text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">
                Cập nhật thông tin thành công!
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Profile Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Thông tin cá nhân</h3>
          {!isEditing ? (
            <button
              type="button"
              onClick={() => setIsEditing(true)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </button>
          ) : (
            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={isSubmitting || isLoading || !isDirty}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <CheckIcon className="h-4 w-4 mr-2" />
                {isSubmitting || isLoading ? 'Đang lưu...' : 'Lưu'}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                <XMarkIcon className="h-4 w-4 mr-2" />
                Hủy
              </button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          {/* Username (Read-only) */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Tên đăng nhập
            </label>
            <input
              {...register('username')}
              type="text"
              disabled
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 cursor-not-allowed sm:text-sm"
            />
            <p className="mt-1 text-xs text-gray-500">Tên đăng nhập không thể thay đổi</p>
          </div>

          {/* Full Name */}
          <div>
            <label htmlFor="fullName" className="block text-sm font-medium text-gray-700">
              Họ và tên
            </label>
            <input
              {...register('fullName')}
              type="text"
              id="fullName"
              disabled={!isEditing}
              className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm ${
                !isEditing ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : ''
              }`}
            />
            {errors.fullName && (
              <p className="mt-1 text-sm text-red-600">{errors.fullName.message}</p>
            )}
          </div>

          {/* Email */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              {...register('email')}
              type="email"
              id="email"
              disabled={!isEditing}
              className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm ${
                !isEditing ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : ''
              }`}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          {/* Phone Number */}
          <div>
            <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
              Số điện thoại
            </label>
            <input
              {...register('phoneNumber')}
              type="tel"
              id="phoneNumber"
              disabled={!isEditing}
              className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm ${
                !isEditing ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : ''
              }`}
            />
            {errors.phoneNumber && (
              <p className="mt-1 text-sm text-red-600">{errors.phoneNumber.message}</p>
            )}
          </div>
        </div>

        {/* Error Message */}
        {(error || errors.root) && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error || errors.root?.message}</p>
          </div>
        )}
      </form>

      {/* Account Information */}
      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Thông tin tài khoản</h3>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Vai trò
            </label>
            <div className="mt-1 flex flex-wrap gap-2">
              {user.roles.map((role, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
                >
                  {role.name}
                </span>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Ngày tham gia
            </label>
            <p className="mt-1 text-sm text-gray-900">
              {new Date(user.createdAt).toLocaleDateString('vi-VN')}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Cập nhật lần cuối
            </label>
            <p className="mt-1 text-sm text-gray-900">
              {new Date(user.updatedAt).toLocaleDateString('vi-VN')}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              ID tài khoản
            </label>
            <p className="mt-1 text-sm text-gray-500 font-mono">
              {user.id}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
