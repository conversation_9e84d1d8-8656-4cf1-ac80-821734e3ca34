import { type ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { USER_ROLES, ROUTES } from '../../utils/constants';
import LoadingSpinner from '../ui/LoadingSpinner';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: 'user' | 'admin';
  redirectTo?: string;
}

export function ProtectedRoute({
  children,
  requiredRole,
  redirectTo
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <Navigate 
        to={redirectTo || ROUTES.LOGIN} 
        state={{ from: location }} 
        replace 
      />
    );
  }

  // Check role-based access
  if (requiredRole && user) {
    const userRoles = user.roles.map(role => role.name.toLowerCase());
    const hasRequiredRole = userRoles.includes(requiredRole.toLowerCase());

    if (!hasRequiredRole) {
      // If user is customer trying to access admin routes, redirect to home
      if (requiredRole === USER_ROLES.ADMIN && userRoles.includes(USER_ROLES.CUSTOMER.toLowerCase())) {
        return <Navigate to={ROUTES.HOME} replace />;
      }

      // If user is admin trying to access customer-only routes, redirect to admin dashboard
      if (requiredRole === 'user' && userRoles.includes(USER_ROLES.ADMIN.toLowerCase())) {
        return <Navigate to={ROUTES.ADMIN.DASHBOARD} replace />;
      }

      // Default redirect to home for other cases
      return <Navigate to={ROUTES.HOME} replace />;
    }
  }

  return <>{children}</>;
}

export default ProtectedRoute;

// Higher-order component for protecting routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: 'user' | 'admin'
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute requiredRole={requiredRole}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// Component for admin-only routes
export function AdminRoute({ children }: { children: ReactNode }) {
  return (
    <ProtectedRoute requiredRole={USER_ROLES.ADMIN}>
      {children}
    </ProtectedRoute>
  );
}

// Component for customer-only routes
export function CustomerRoute({ children }: { children: ReactNode }) {
  return (
    <ProtectedRoute requiredRole={USER_ROLES.CUSTOMER}>
      {children}
    </ProtectedRoute>
  );
}

// Component for public routes that redirect authenticated users
export function PublicRoute({ 
  children, 
  redirectTo 
}: { 
  children: ReactNode;
  redirectTo?: string;
}) {
  const { isAuthenticated, isLoading, user } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Redirect authenticated users
  if (isAuthenticated) {
    const userRoles = user?.roles?.map(role => role.name.toLowerCase()) || [];
    const isAdmin = userRoles.includes(USER_ROLES.ADMIN.toLowerCase());
    const defaultRedirect = isAdmin ? ROUTES.ADMIN.DASHBOARD : ROUTES.HOME;

    return <Navigate to={redirectTo || defaultRedirect} replace />;
  }

  return <>{children}</>;
}
