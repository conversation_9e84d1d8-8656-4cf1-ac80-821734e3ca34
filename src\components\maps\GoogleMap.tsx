import { useEffect } from 'react';
import { MapPinIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { useGoogleMaps, type MapLocation } from '../../hooks/useGoogleMaps';
import { MapFallback } from './MapFallback';

interface GoogleMapProps {
  center?: MapLocation;
  zoom?: number;
  onLocationSelect?: (location: MapLocation) => void;
  enableGeolocation?: boolean;
  height?: string;
  className?: string;
  showInstructions?: boolean;
  apiKey?: string;
}

export function GoogleMap({
  center,
  zoom,
  onLocationSelect,
  enableGeolocation = true,
  height = 'h-96',
  className = '',
  showInstructions = true,
  apiKey,
}: GoogleMapProps) {
  const {
    mapRef,
    isLoading,
    error,
    selectedLocation,
    initializeMap,
  } = useGoogleMaps({
    center,
    zoom,
    onLocationSelect,
    enableGeolocation,
    apiKey,
  });

  useEffect(() => {
    initializeMap();
  }, [initializeMap]);

  if (error) {
    // If error is about API key, show fallback
    if (error.includes('API key')) {
      return (
        <MapFallback
          onLocationSelect={onLocationSelect}
          height={height}
          className={className}
        />
      );
    }

    // For other errors, show error message
    return (
      <div className={`${height} ${className} bg-red-50 border border-red-200 rounded-lg flex items-center justify-center`}>
        <div className="text-center p-6">
          <ExclamationTriangleIcon className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-red-800 mb-2">
            Không thể tải bản đồ
          </h3>
          <p className="text-sm text-red-600 mb-4">
            {error}
          </p>
          <div className="text-xs text-red-500 bg-red-100 rounded p-2">
            <p className="font-medium mb-1">Hướng dẫn khắc phục:</p>
            <ul className="text-left list-disc list-inside space-y-1">
              <li>Kiểm tra kết nối internet</li>
              <li>Thử tải lại trang</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Map Container */}
      <div
        ref={mapRef}
        className={`w-full ${height} rounded-lg border border-gray-300 bg-gray-100`}
      />
      
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Đang tải bản đồ...</p>
          </div>
        </div>
      )}

      {/* Instructions */}
      {showInstructions && !isLoading && !error && (
        <div className="mt-3 bg-blue-50 border border-blue-200 rounded-md p-3">
          <div className="flex">
            <MapPinIcon className="h-5 w-5 text-blue-400 mt-0.5 mr-2 flex-shrink-0" />
            <div className="text-sm text-blue-700">
              <p className="font-medium">Hướng dẫn:</p>
              <ul className="mt-1 list-disc list-inside space-y-1">
                <li>Click vào bản đồ để chọn địa điểm</li>
                <li>Kéo thả marker để điều chỉnh vị trí chính xác</li>
                <li>Địa chỉ sẽ được tự động điền</li>
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Selected Location Info */}
      {selectedLocation && (
        <div className="mt-3 bg-green-50 border border-green-200 rounded-md p-3">
          <div className="flex">
            <MapPinIcon className="h-5 w-5 text-green-400 mt-0.5 mr-2 flex-shrink-0" />
            <div className="text-sm text-green-700">
              <p className="font-medium">Vị trí đã chọn:</p>
              <p className="mt-1">Latitude: {selectedLocation.lat.toFixed(6)}</p>
              <p>Longitude: {selectedLocation.lng.toFixed(6)}</p>
              {selectedLocation.address && (
                <p className="mt-1 text-xs">{selectedLocation.address}</p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
