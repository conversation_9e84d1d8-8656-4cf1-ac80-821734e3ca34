import { Link } from 'react-router-dom';
import { ShoppingCartIcon, EyeIcon } from '@heroicons/react/24/outline';
import type { Pizza } from '../../services/pizza.service';

interface PizzaCardProps {
  pizza: Pizza;
  onAddToCart?: (pizza: Pizza) => void;
  className?: string;
}

export function PizzaCard({ pizza, onAddToCart, className = '' }: PizzaCardProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onAddToCart?.(pizza);
  };

  return (
    <div className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden ${className}`}>
      <Link to={`/pizza/${pizza.id}`} className="block">
        {/* Pizza Image */}
        <div className="relative h-48 bg-gray-200">
          {pizza.image ? (
            <img
              src={pizza.image}
              alt={pizza.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-400">
              <span className="text-6xl">🍕</span>
            </div>
          )}
          
          {/* Availability Badge */}
          {pizza.isAvailable === false && (
            <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              Hết hàng
            </div>
          )}
          
          {/* Size Badge */}
          {pizza.size && (
            <div className="absolute top-2 left-2 bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium capitalize">
              {pizza.size}
            </div>
          )}
        </div>

        {/* Pizza Info */}
        <div className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
            {pizza.name}
          </h3>
          
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {pizza.description}
          </p>

          {/* Ingredients */}
          {pizza.ingredients && pizza.ingredients.length > 0 && (
            <div className="mb-3">
              <p className="text-xs text-gray-500 mb-1">Nguyên liệu:</p>
              <div className="flex flex-wrap gap-1">
                {pizza.ingredients.slice(0, 3).map((ingredient, index) => (
                  <span
                    key={index}
                    className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
                  >
                    {ingredient}
                  </span>
                ))}
                {pizza.ingredients.length > 3 && (
                  <span className="text-xs text-gray-500">
                    +{pizza.ingredients.length - 3} khác
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Category */}
          {pizza.category && (
            <div className="mb-3">
              <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                {pizza.category}
              </span>
            </div>
          )}

          {/* Price and Actions */}
          <div className="flex items-center justify-between">
            <div className="text-xl font-bold text-orange-600">
              {formatPrice(pizza.price)}
            </div>
            
            <div className="flex space-x-2">
              {/* View Details Button */}
              <button className="p-2 text-gray-500 hover:text-orange-600 transition-colors">
                <EyeIcon className="h-5 w-5" />
              </button>
              
              {/* Add to Cart Button */}
              {pizza.isAvailable !== false && onAddToCart && (
                <button
                  onClick={handleAddToCart}
                  className="p-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                  title="Thêm vào giỏ hàng"
                >
                  <ShoppingCartIcon className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
}
