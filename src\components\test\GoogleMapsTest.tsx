import { useState } from 'react';
import { GoogleMap } from '../maps/GoogleMap';
import { addressService } from '../../services/address.service';
import type { MapLocation } from '../../hooks/useGoogleMaps';

const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || 'AIzaSyCFTgWDocizT3d4PZYqj_gPNfbnEA39HNw';

export function GoogleMapsTest() {
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null);
  const [label, setLabel] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleLocationSelect = (location: MapLocation) => {
    setSelectedLocation(location);
    console.log('Selected location:', location);
  };

  const handleSaveAddress = async () => {
    if (!selectedLocation || !label.trim()) {
      setMessage('Vui lòng chọn vị trí và nhập nhãn địa chỉ');
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      const addressData = {
        label: label.trim(),
        fullAddress: selectedLocation.address || `${selectedLocation.lat}, ${selectedLocation.lng}`,
        latitude: selectedLocation.lat,
        longitude: selectedLocation.lng,
      };

      console.log('Saving address:', addressData);
      
      const result = await addressService.createAddress(addressData);
      
      console.log('Address saved successfully:', result);
      setMessage(`Địa chỉ đã được lưu thành công! ID: ${result.id}`);
      setLabel('');
      setSelectedLocation(null);
    } catch (error: any) {
      console.error('Error saving address:', error);
      setMessage(`Lỗi: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Google Maps Test</h1>
      
      {/* Google Map */}
      <div className="mb-6">
        <GoogleMap
          onLocationSelect={handleLocationSelect}
          height="h-96"
          apiKey={GOOGLE_MAPS_API_KEY}
          center={{ lat: 16.0544, lng: 108.2022 }} // Đà Nẵng
        />
      </div>

      {/* Selected Location Info */}
      {selectedLocation && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">Vị trí đã chọn:</h3>
          <p><strong>Latitude:</strong> {selectedLocation.lat}</p>
          <p><strong>Longitude:</strong> {selectedLocation.lng}</p>
          {selectedLocation.address && (
            <p><strong>Địa chỉ:</strong> {selectedLocation.address}</p>
          )}
        </div>
      )}

      {/* Address Form */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Nhãn địa chỉ:
        </label>
        <input
          type="text"
          value={label}
          onChange={(e) => setLabel(e.target.value)}
          placeholder="Ví dụ: Nhà riêng, Văn phòng..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Save Button */}
      <button
        onClick={handleSaveAddress}
        disabled={!selectedLocation || !label.trim() || isLoading}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
      >
        {isLoading ? 'Đang lưu...' : 'Lưu địa chỉ'}
      </button>

      {/* Message */}
      {message && (
        <div className={`mt-4 p-3 rounded-md ${
          message.includes('Lỗi') 
            ? 'bg-red-50 border border-red-200 text-red-700'
            : 'bg-green-50 border border-green-200 text-green-700'
        }`}>
          {message}
        </div>
      )}
    </div>
  );
}
