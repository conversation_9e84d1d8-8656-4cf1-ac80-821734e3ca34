import { useRef, useState } from 'react';
import { Loader } from '@googlemaps/js-api-loader';

export interface MapLocation {
  lat: number;
  lng: number;
  address?: string;
}

interface UseGoogleMapsOptions {
  center?: MapLocation;
  zoom?: number;
  onLocationSelect?: (location: MapLocation) => void;
  enableGeolocation?: boolean;
  apiKey?: string;
}

export function useGoogleMaps(options: UseGoogleMapsOptions = {}) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [marker, setMarker] = useState<google.maps.Marker | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null);

  const {
    center = { lat: 10.7769, lng: 106.7009 }, // Default to Ho Chi Minh City
    zoom = 15,
    onLocationSelect,
    enableGeolocation = true,
    apiKey,
  } = options;

  // Initialize Google Maps
  const initializeMap = async () => {
    if (!mapRef.current) return;

    setIsLoading(true);
    setError(null);

    try {
      // Check if API key is available
      const googleApiKey = apiKey || import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
      if (!googleApiKey || googleApiKey === 'YOUR_GOOGLE_MAPS_API_KEY_HERE') {
        throw new Error('Google Maps API key is not configured. Please add VITE_GOOGLE_MAPS_API_KEY to your .env file.');
      }

      const loader = new Loader({
        apiKey: googleApiKey,
        version: 'weekly',
        libraries: ['places', 'geometry'],
      });

      await loader.load();

      const mapInstance = new google.maps.Map(mapRef.current, {
        center,
        zoom,
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: false,
        zoomControl: true,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
      });

      setMap(mapInstance);

      // Add click listener to map
      mapInstance.addListener('click', (event: google.maps.MapMouseEvent) => {
        if (event.latLng) {
          const lat = event.latLng.lat();
          const lng = event.latLng.lng();
          handleLocationSelect(lat, lng, mapInstance);
        }
      });

      // Try to get user's current location if enabled
      if (enableGeolocation && navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const userLocation = {
              lat: position.coords.latitude,
              lng: position.coords.longitude,
            };
            mapInstance.setCenter(userLocation);
            handleLocationSelect(userLocation.lat, userLocation.lng, mapInstance);
          },
          (error) => {
            console.warn('Geolocation failed:', error);
            // Use default location
            handleLocationSelect(center.lat, center.lng, mapInstance);
          }
        );
      } else {
        handleLocationSelect(center.lat, center.lng, mapInstance);
      }
    } catch (error: any) {
      console.error('Error loading Google Maps:', error);
      setError(error.message || 'Failed to load Google Maps');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLocationSelect = async (lat: number, lng: number, mapInstance: google.maps.Map) => {
    // Remove existing marker
    if (marker) {
      marker.setMap(null);
    }

    // Create new marker
    const newMarker = new google.maps.Marker({
      position: { lat, lng },
      map: mapInstance,
      draggable: true,
      title: 'Địa chỉ đã chọn',
      animation: google.maps.Animation.DROP,
    });

    // Add drag listener to marker
    newMarker.addListener('dragend', () => {
      const position = newMarker.getPosition();
      if (position) {
        handleLocationSelect(position.lat(), position.lng(), mapInstance);
      }
    });

    setMarker(newMarker);

    // Reverse geocoding to get address
    try {
      const geocoder = new google.maps.Geocoder();
      let address = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;

      geocoder.geocode({ location: { lat, lng } }, (results, status) => {
        if (status === google.maps.GeocoderStatus.OK && results[0]) {
          address = results[0].formatted_address;
        }
      });

      const location: MapLocation = { lat, lng, address };
      setSelectedLocation(location);
      onLocationSelect?.(location);
    } catch (error) {
      console.error('Geocoding error:', error);
      const location: MapLocation = { lat, lng, address: `${lat.toFixed(6)}, ${lng.toFixed(6)}` };
      setSelectedLocation(location);
      onLocationSelect?.(location);
    }
  };

  const setMapCenter = (location: MapLocation) => {
    if (map) {
      map.setCenter({ lat: location.lat, lng: location.lng });
      handleLocationSelect(location.lat, location.lng, map);
    }
  };

  const clearMarker = () => {
    if (marker) {
      marker.setMap(null);
      setMarker(null);
    }
    setSelectedLocation(null);
  };

  return {
    mapRef,
    map,
    marker,
    isLoading,
    error,
    selectedLocation,
    initializeMap,
    setMapCenter,
    clearMarker,
  };
}
