import { useState, useEffect, useCallback } from 'react';
import { pizzaService, type Pizza, type PizzaFilters } from '../services/pizza.service';

interface UsePizzaReturn {
  pizzas: Pizza[];
  loading: boolean;
  error: string | null;
  fetchPizzas: (filters?: PizzaFilters) => Promise<void>;
  searchPizzas: (query: string) => Promise<void>;
  clearError: () => void;
  refetch: () => Promise<void>;
}

interface UsePizzaByIdReturn {
  pizza: Pizza | null;
  loading: boolean;
  error: string | null;
  fetchPizza: (id: string) => Promise<void>;
  clearError: () => void;
  refetch: () => Promise<void>;
}

// Hook for getting all pizzas
export function usePizza(initialFilters?: PizzaFilters): UsePizzaReturn {
  const [pizzas, setPizzas] = useState<Pizza[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentFilters, setCurrentFilters] = useState<PizzaFilters | undefined>(initialFilters);

  const fetchPizzas = useCallback(async (filters?: PizzaFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      const filtersToUse = filters || currentFilters;
      setCurrentFilters(filtersToUse);
      
      const result = await pizzaService.getAllPizzas(filtersToUse);
      setPizzas(result);
    } catch (err: any) {
      setError(err.message);
      setPizzas([]);
    } finally {
      setLoading(false);
    }
  }, [currentFilters]);

  const searchPizzas = useCallback(async (query: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await pizzaService.searchPizzas(query);
      setPizzas(result);
      setCurrentFilters({ search: query });
    } catch (err: any) {
      setError(err.message);
      setPizzas([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refetch = useCallback(async () => {
    await fetchPizzas(currentFilters);
  }, [fetchPizzas, currentFilters]);

  // Auto-fetch on mount
  useEffect(() => {
    fetchPizzas();
  }, []);

  return {
    pizzas,
    loading,
    error,
    fetchPizzas,
    searchPizzas,
    clearError,
    refetch,
  };
}

// Hook for getting pizza by ID
export function usePizzaById(initialId?: string): UsePizzaByIdReturn {
  const [pizza, setPizza] = useState<Pizza | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentId, setCurrentId] = useState<string | undefined>(initialId);

  const fetchPizza = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    setCurrentId(id);
    
    try {
      const result = await pizzaService.getPizzaById(id);
      setPizza(result);
    } catch (err: any) {
      setError(err.message);
      setPizza(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refetch = useCallback(async () => {
    if (currentId) {
      await fetchPizza(currentId);
    }
  }, [fetchPizza, currentId]);

  // Auto-fetch on mount if ID is provided
  useEffect(() => {
    if (initialId) {
      fetchPizza(initialId);
    }
  }, [initialId, fetchPizza]);

  return {
    pizza,
    loading,
    error,
    fetchPizza,
    clearError,
    refetch,
  };
}
