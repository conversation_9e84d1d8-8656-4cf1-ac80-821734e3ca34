{"common": {"loading": "Loading...", "error": "An error occurred", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "add": "Add", "search": "Search", "filter": "Filter", "sort": "Sort", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "close": "Close", "view": "View", "download": "Download", "upload": "Upload", "select": "Select", "clear": "Clear", "reset": "Reset", "comingSoon": "Feature will be added in the next version", "phone": "Phone", "address": "Address"}, "nav": {"home": "Home", "menu": "<PERSON><PERSON>", "about": "About Us", "contact": "Contact", "cart": "<PERSON><PERSON>", "login": "<PERSON><PERSON>", "register": "Sign Up", "logout": "Logout", "profile": "Account", "orders": "Orders", "addresses": "Addresses", "wishlist": "Wishlist"}, "auth": {"loginRequired": "Login to save and manage your delivery addresses", "pleaseLogin": "Please login to use this feature", "login": {"title": "Sign in to your account", "subtitle": "Or", "createAccount": "create a new account", "email": "Email address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot your password?", "signIn": "Sign in", "continueWith": "Or continue with", "google": "Google", "facebook": "Facebook"}, "register": {"title": "Create Account", "subtitle": "Join Pizza Mỹ today", "username": "Username", "fullName": "Full Name", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phone": "Phone Number", "password": "Password", "confirmPassword": "Confirm Password", "agreeTerms": "I agree to the", "termsConditions": "Terms and Conditions", "privacyPolicy": "Privacy Policy", "createAccount": "Create Account", "signIn": "Sign In", "signInExisting": "sign in to your existing account", "registerSuccess": "Registration successful!", "registerError": "An error occurred during registration"}}, "home": {"hero": {"title": "Delicious Pizza", "subtitle": "Delivered Hot", "description": "Experience the best pizza in town with fresh ingredients and authentic flavors. Order now and get it delivered to your doorstep!", "orderNow": "Order Now", "learnMore": "Learn More"}, "categories": {"title": "Shop by Category", "description": "Discover our delicious menu categories", "pizza": "Pizza", "beverages": "Beverages", "desserts": "Desserts"}, "featured": {"title": "Featured Products", "description": "Try our most popular and delicious pizzas", "viewDetails": "View Details", "viewAll": "View All Products"}, "cta": {"title": "Ready to Order?", "description": "Join thousands of satisfied customers who love our delicious pizzas. Order now and get free delivery on orders over $25!", "startOrdering": "Start Ordering"}}, "products": {"margherita": {"name": "Margherita Pizza", "description": "Classic pizza with fresh mozzarella, tomatoes, and basil"}, "pepperoni": {"name": "Pepperoni Pizza", "description": "Traditional pepperoni with mozzarella cheese"}, "supreme": {"name": "Supreme Pizza", "description": "Loaded with pepperoni, sausage, peppers, and onions"}}, "footer": {"description": "Delicious pizzas made with fresh ingredients and delivered hot to your door. Experience the best pizza in town!", "sections": {"shop": "Shop", "customerService": "Customer Service", "company": "Company", "legal": "Legal"}, "address": ["23 <PERSON><PERSON> <PERSON><PERSON>", "An Hai Bac", "<PERSON>, <PERSON>", "Vietnam"], "links": {"allProducts": "All Products", "contactUs": "Contact Us", "faq": "FAQ", "shipping": "Shipping Info", "returns": "Returns", "aboutUs": "About Us", "careers": "Careers", "press": "Press", "blog": "Blog", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>", "refund": "Refund Policy"}, "newsletter": {"title": "Subscribe to our newsletter", "description": "Get the latest updates on new products and upcoming sales", "placeholder": "Enter your email", "subscribe": "Subscribe"}, "copyright": "© {{year}} Pizza My Shop. All rights reserved.", "madeWith": "Made with ❤️ for pizza lovers"}, "admin": {"dashboard": "Dashboard", "menu": "<PERSON><PERSON>", "categories": "Categories", "orders": "Orders", "customers": "Customers", "analytics": "Analytics", "settings": "Settings", "viewStore": "View Store", "adminPanel": "Admin Panel"}, "errors": {"404": {"title": "404", "message": "Page not found"}, "network": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access denied.", "notFound": "The requested resource was not found.", "serverError": "Internal server error. Please try again later.", "validationError": "Please check your input and try again.", "tokenExpired": "Your session has expired. Please log in again."}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Must be at least {{min}} characters", "maxLength": "Must not exceed {{max}} characters", "passwordMatch": "Passwords don't match", "passwordStrength": "Password must contain at least one uppercase letter, one lowercase letter, and one number", "phone": "Please enter a valid phone number", "agreeTerms": "You must agree to the terms and conditions"}, "menu": {"title": "Pizza Mỹ Menu", "subtitle": "Discover our most delicious dishes", "categories": {"all": "All", "vegetarian": "Vegetarian Pizza", "meat": "Meat Pizza", "combo": "Combo"}, "filters": {"category": "Category", "price": "Price", "rating": "Rating", "sortBy": "Sort by", "priceAsc": "Price: Low to High", "priceDesc": "Price: High to Low", "nameAsc": "Name: A-<PERSON>", "nameDesc": "Name: Z-<PERSON>", "ratingDesc": "Highest Rated"}, "product": {"addToCart": "Add to Cart", "viewDetails": "View Details", "outOfStock": "Out of Stock", "rating": "Rating", "reviews": "reviews"}}, "profile": {"title": "Account Management", "subtitle": "Manage your account information and settings", "tabs": {"profile": "Personal Info", "addresses": "Addresses", "orders": "Orders"}, "personalInfo": {"title": "Personal Information", "fullName": "Full Name", "email": "Email", "phoneNumber": "Phone Number", "username": "Username", "updateProfile": "Update Profile", "changePassword": "Change Password"}, "addresses": {"title": "Address Management", "addNew": "Add New Address", "editAddress": "Edit Address", "deleteAddress": "Delete Address", "setDefault": "Set as <PERSON><PERSON><PERSON>", "label": "Address Label", "fullAddress": "Full Address", "selectOnMap": "Select location on map", "viewOnMap": "View on Map", "noAddresses": "No addresses yet", "addFirstAddress": "Add your first address"}, "orders": {"title": "Order History", "orderNumber": "Order Number", "orderDate": "Order Date", "status": "Status", "total": "Total", "viewDetails": "View Details", "reorder": "Reorder", "cancel": "Cancel", "noOrders": "No orders yet", "startShopping": "Start Shopping"}}, "address": {"title": "Delivery Addresses", "subtitle": "Manage delivery addresses to order pizza quickly and conveniently", "addNew": "Add New Address", "selectLocation": "Select location on map", "instructions": {"title": "Instructions:", "clickMap": "Click on the map to select a location", "dragMarker": "Drag the marker to adjust the exact position", "autoFill": "Address will be automatically filled"}, "form": {"label": "Address Label", "labelPlaceholder": "e.g., Home, Office, Friend's place...", "fullAddress": "Full Address", "fullAddressPlaceholder": "Address will be automatically filled when you select on the map"}, "map": {"loading": "Loading map...", "error": "Cannot load map", "noApiKey": "Google Maps not available", "apiKeyMessage": "API key not configured. Please enter coordinates manually.", "manualInput": "Manual coordinate input", "latitude": "Latitude", "longitude": "Longitude", "currentLocation": "Current Location", "defaultLocation": "Ho Chi Minh City", "selectPosition": "Select Position", "selectedLocation": "Selected Location:"}, "actions": {"save": "Save Address", "saving": "Saving...", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "viewOnMap": "View on Map", "confirmDelete": "Are you sure you want to delete this address?"}, "messages": {"selectLocationFirst": "Please select a location on the map", "addedSuccessfully": "Address added successfully!", "updatedSuccessfully": "Address updated successfully!", "deletedSuccessfully": "Address deleted successfully!", "errorAdding": "Error occurred while adding address", "errorUpdating": "Error occurred while updating address", "errorDeleting": "Error occurred while deleting address", "errorLoading": "Error occurred while loading address list"}}, "language": {"title": "Language", "vietnamese": "Tiếng <PERSON>", "english": "English", "switchTo": "Switch to {{language}}"}, "homepage": {"hero": {"title": "We Deliver Pizza Anywhere in Da Nang City", "subtitle": "Super Extra Large 46 cm Pizza - Best Value in Da Nang", "cta": "Order Now", "viewMenu": "View Menu"}, "features": {"title": "Why Choose Pizza Mỹ?", "freshIngredients": {"title": "Fresh Ingredients", "description": "We only use the freshest and finest ingredients"}, "fastDelivery": {"title": "Fast Delivery", "description": "Delivered within 30 minutes or it's free"}, "bestPrice": {"title": "Best Price", "description": "High quality at the most affordable prices"}}, "testimonials": {"title": "What Our Customers Say", "customer1": {"name": "<PERSON>", "text": "The pizza here is absolutely delicious! I've tried many places but Pizza Mỹ is still number 1."}, "customer2": {"name": "<PERSON>", "text": "Fast delivery, pizza still hot. Staff are very friendly and professional."}, "customer3": {"name": "<PERSON>", "text": "Reasonable prices, excellent quality. My family loves Pizza Mỹ."}}, "cta": {"title": "Ready to Enjoy Delicious Pizza?", "subtitle": "Order today and get special offers", "orderNow": "Order Now"}}, "about": {"title": "About Us", "hero": {"title": "PizzaMy - Big Pizza, Big Flavor", "subtitle": "Bringing bold, satisfying flavors of classic American pizza to the heart of Da Nang"}, "story": {"title": "Our Story", "paragraph1": "PizzaMy is all about bringing the bold, satisfying flavors of classic American pizza to the heart of Da Nang. Established in 2025, we specialize in massive 18-inch (46 cm) pizzas, perfect for sharing with friends and family.", "paragraph2": "Each pizza is made fresh to order using the highest-quality ingredients, from our handcrafted dough to our savory sauces and premium toppings. Whether you're feeding a group of four or just craving something big and delicious, PizzaMy delivers.", "paragraph3": "We offer fast, reliable delivery anywhere in Da Nang City, so you can enjoy hot, fresh pizza wherever you are. At PizzaMy, it’s simple: Big pizza. Big flavor. Big value."}, "stats": {"title": "Our Journey Begins", "subtitle": "Our first steps", "year": "2025", "yearLabel": "Starting Year", "location": "<PERSON>", "locationLabel": "First City", "mission": "Nationwide", "missionLabel": "Expansion Vision", "goal": "No. 1", "goalLabel": "Leadership Goal"}, "features": {"title": "Why Choose PizzaMy?", "subtitle": "Core values that make the PizzaMy brand", "freshIngredients": {"title": "Premium Ingredients", "description": "We use the highest-quality ingredients, from handcrafted dough to savory sauces and premium toppings"}, "fastDelivery": {"title": "Fast Delivery", "description": "Fast, reliable delivery anywhere in Da Nang City to bring you hot, fresh pizza"}, "bestPrice": {"title": "Big Value", "description": "Massive 18-inch pizzas at affordable prices, perfect for sharing with friends and family"}, "quality": {"title": "Quality Assurance", "description": "Each pizza is made fresh to order, ensuring top quality and bold American flavors"}, "professional": {"title": "Expert Team", "description": "Our team is dedicated to crafting authentic American pizzas with care and expertise"}, "expansion": {"title": "Expansion Vision", "description": "Starting in Da Nang, we aim to bring our big pizzas to every corner of Vietnam"}}, "mission": {"title": "Mission & Vision", "vision": {"title": "Vision", "description": "To become the leading American pizza brand in Vietnam, delivering big pizzas with bold flavors to every corner of the country."}, "mission": {"title": "Mission", "description": "From Da Nang, we aim to share massive, delicious American pizzas with top quality and fast delivery."}, "values": {"title": "Core Values", "description": "Quality - Authenticity - Value - Customer Satisfaction."}}, "contact": {"title": "Contact Us", "subtitle": "Get in touch for big pizza and big flavor!", "description": "Reach out to enjoy our massive 18-inch pizzas or learn more about PizzaMy!", "hotline": "Hotline: 0905759986", "email": "Instagram: @maisyshare9x", "address": " 23 <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>"}}, "comingSoon": {"title": "Coming Soon", "subtitle": "Under Development", "description": "Pizza Mỹ website is currently under development and updating", "subDescription": "We are working hard to bring you the best experience!", "features": {"newUI": {"title": "New Interface", "description": "Modern and user-friendly UI/UX"}, "newFeatures": {"title": "New Features", "description": "Fast and convenient ordering"}, "launchSoon": {"title": "Launching Soon", "description": "Follow for latest updates"}}, "progress": "Development Progress", "backHome": "Back to Home", "notify": "Get Notified", "follow": "Follow us for latest updates", "copyright": "© 2025 Pizza Mỹ - Da Nang, Vietnam"}}