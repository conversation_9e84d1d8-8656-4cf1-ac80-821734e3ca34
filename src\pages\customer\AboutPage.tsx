import { useTranslation } from 'react-i18next';
import {
  HeartIcon,
  TruckIcon,
  StarIcon,
  UsersIcon,
  GlobeAsiaAustraliaIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

export default function AboutPage() {
  const { t } = useTranslation();

  const features = [
    {
      icon: HeartIcon,
      title: t('about.features.freshIngredients.title'),
      description: t('about.features.freshIngredients.description'),
    },
    {
      icon: TruckIcon,
      title: t('about.features.fastDelivery.title'),
      description: t('about.features.fastDelivery.description'),
    },
    {
      icon: StarIcon,
      title: t('about.features.quality.title'),
      description: t('about.features.quality.description'),
    },
    {
      icon: UsersIcon,
      title: t('about.features.professional.title'),
      description: t('about.features.professional.description'),
    },
    {
      icon: GlobeAsiaAustraliaIcon,
      title: t('about.features.expansion.title'),
      description: t('about.features.expansion.description'),
    },
    {
      icon: SparklesIcon,
      title: t('about.features.bestPrice.title'),
      description: t('about.features.bestPrice.description'),
    },
  ];

  const stats = [
    { number: t('about.stats.year'), label: t('about.stats.yearLabel') },
    { number: t('about.stats.location'), label: t('about.stats.locationLabel') },
    { number: t('about.stats.mission'), label: t('about.stats.missionLabel') },
    { number: t('about.stats.goal'), label: t('about.stats.goalLabel') },
  ];

  return (
    <div className="bg-white overflow-hidden">
      {/* Hero Section with Animated Background */}
      <section className="relative bg-gradient-to-br from-red-600 via-red-500 to-red-700 text-white py-24 overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full animate-pulse"></div>
          <div className="absolute top-32 right-20 w-24 h-24 bg-white/5 rounded-full animate-bounce delay-300"></div>
          <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-white/5 rounded-full animate-pulse delay-700"></div>
          <div className="absolute bottom-32 right-1/3 w-20 h-20 bg-white/10 rounded-full animate-bounce delay-1000"></div>

          {/* Pizza slice decorations */}
          <div className="absolute top-1/4 left-1/2 transform -translate-x-1/2 opacity-10">
            <div className="text-8xl animate-spin-slow">🍕</div>
          </div>
        </div>

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-red-800/30 to-transparent"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in-up">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-red-100 bg-clip-text text-transparent">
              {t('about.hero.title')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-4xl mx-auto leading-relaxed text-red-50">
              {t('about.hero.subtitle')}
            </p>
            <div className="inline-block animate-bounce-slow">
              <div className="text-6xl mb-4">🇺🇸</div>
              <p className="text-lg font-medium">Authentic American Pizza</p>
            </div>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20 bg-gradient-to-b from-white to-red-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="animate-fade-in-left">
              <div className="inline-flex items-center bg-red-100 text-red-600 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <SparklesIcon className="h-4 w-4 mr-2" />
                {t('about.story.title')}
              </div>
              <h2 className="text-4xl font-bold text-gray-900 mb-8 leading-tight">
                {t('about.story.title')} <span className="text-red-600">🇻🇳</span>
              </h2>
              <div className="space-y-6 text-gray-600 text-lg leading-relaxed">
                <p className="hover:text-gray-800 transition-colors duration-300">
                  {t('about.story.paragraph1')}
                </p>
                <p className="hover:text-gray-800 transition-colors duration-300">
                  {t('about.story.paragraph2')}
                </p>
                <p className="hover:text-gray-800 transition-colors duration-300">
                  {t('about.story.paragraph3')}
                </p>
              </div>

              {/* Call to Action */}
              <div className="mt-8 flex items-center space-x-4">
                <div className="flex items-center text-red-600">
                  <div className="w-3 h-3 bg-red-600 rounded-full animate-pulse mr-3"></div>
                  <span className="font-semibold">{t('about.stats.missionLabel')}...</span>
                </div>
              </div>
            </div>

            <div className="relative animate-fade-in-right">
              {/* Main Image */}
              <div className="relative group">
                <img
                  src="/assets/logo.png"
                  alt="Pizza Mỹ Logo"
                  className="rounded-2xl shadow-2xl w-full h-96 object-contain bg-white p-8 transform group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-red-900/40 via-transparent to-transparent rounded-2xl"></div>

                {/* Floating Badge */}
                <div className="absolute -top-4 -right-4 bg-white rounded-full p-4 shadow-lg animate-bounce-slow">
                  <div className="text-2xl">🇺🇸</div>
                </div>

                {/* Location Badge */}
                <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
                  <div className="flex items-center text-red-600">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-2 animate-pulse"></div>
                    <span className="font-semibold text-sm">{t('about.contact.address')}</span>
                  </div>
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute -z-10 top-8 left-8 w-full h-full bg-red-200 rounded-2xl opacity-30"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-r from-red-600 to-red-700 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full"
               style={{
                 backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
               }}>
          </div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in-up">
            <div className="inline-flex items-center bg-white/10 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
              <StarIcon className="h-4 w-4 mr-2" />
              {t('about.stats.title')}
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-4">
              {t('about.stats.subtitle')}
            </h2>
            <div className="w-24 h-1 bg-white mx-auto rounded-full"></div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="text-center group animate-fade-in-up"
                style={{ animationDelay: `${index * 200}ms` }}
              >
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 hover:bg-white/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                  <div className="text-4xl md:text-6xl font-bold text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    {stat.number}
                  </div>
                  <div className="text-red-100 font-medium text-lg">
                    {stat.label}
                  </div>
                  <div className="mt-4 w-12 h-1 bg-white/30 mx-auto rounded-full group-hover:bg-white/60 transition-colors duration-300"></div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-16">
            <div className="inline-flex items-center text-red-100">
              <div className="w-8 h-px bg-red-300 mr-4"></div>
              <span className="text-sm font-medium">{t('about.stats.subtitle')}</span>
              <div className="w-8 h-px bg-red-300 ml-4"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in-up">
            <div className="inline-flex items-center bg-red-100 text-red-600 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <HeartIcon className="h-4 w-4 mr-2" />
              {t('about.features.title')}
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {t('about.features.subtitle')}
            </h2>
            <div className="w-24 h-1 bg-red-600 mx-auto rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="group animate-fade-in-up hover:animate-none"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 hover:border-red-200">
                  <div className="absolute inset-0 bg-gradient-to-br from-red-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>

                  <div className="relative">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                      <feature.icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-red-600 transition-colors duration-300">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                      {feature.description}
                    </p>
                    <div className="mt-6 w-12 h-1 bg-red-200 group-hover:bg-red-500 group-hover:w-16 transition-all duration-300 rounded-full"></div>
                  </div>
                  <div className="absolute top-4 right-4 w-2 h-2 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-gradient-to-b from-red-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in-up">
            <div className="inline-flex items-center bg-red-100 text-red-600 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <GlobeAsiaAustraliaIcon className="h-4 w-4 mr-2" />
              {t('about.mission.title')}
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {t('about.mission.title')}
            </h2>
            <div className="w-24 h-1 bg-red-600 mx-auto rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="group animate-fade-in-up" style={{ animationDelay: '0ms' }}>
              <div className="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border-2 border-red-100 hover:border-red-300">
                <div className="absolute -top-4 left-8 bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                  {t('about.mission.vision.title')}
                </div>
                <div className="mt-4">
                  <div className="text-4xl mb-4">🎯</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-red-600 transition-colors duration-300">
                    {t('about.mission.vision.title')}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {t('about.mission.vision.description')}
                  </p>
                </div>
              </div>
            </div>

            <div className="group animate-fade-in-up" style={{ animationDelay: '200ms' }}>
              <div className="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border-2 border-red-100 hover:border-red-300">
                <div className="absolute -top-4 left-8 bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                  {t('about.mission.mission.title')}
                </div>
                <div className="mt-4">
                  <div className="text-4xl mb-4">🚀</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-red-600 transition-colors duration-300">
                    {t('about.mission.mission.title')}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {t('about.mission.mission.description')}
                  </p>
                </div>
              </div>
            </div>

            <div className="group animate-fade-in-up" style={{ animationDelay: '400ms' }}>
              <div className="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border-2 border-red-100 hover:border-red-300">
                <div className="absolute -top-4 left-8 bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                  {t('about.mission.values.title')}
                </div>
                <div className="mt-4">
                  <div className="text-4xl mb-4">❤️</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-red-600 transition-colors duration-300">
                    {t('about.mission.values.title')}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {t('about.mission.values.description')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-20 bg-gradient-to-r from-red-600 via-red-700 to-red-800 text-white relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white/5 rounded-full animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-40 h-40 bg-white/5 rounded-full animate-pulse delay-700"></div>
          <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-white/10 rounded-full animate-bounce delay-300"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in-up">
            <div className="inline-flex items-center bg-white/10 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
              <UsersIcon className="h-4 w-4 mr-2" />
              {t('about.contact.title')}
            </div>

            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              {t('about.contact.subtitle')}
            </h2>

            <p className="text-xl mb-12 max-w-3xl mx-auto text-red-100 leading-relaxed">
              {t('about.contact.description')}
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 transform hover:scale-105">
                <div className="text-3xl mb-3">📞</div>
                <h3 className="font-bold mb-2">{t('common.phone')}</h3>
                <p className="text-red-100">0905759986</p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 transform hover:scale-105">
                <div className="text-3xl mb-3">✉️</div>
                <h3 className="font-bold mb-2">Instagram</h3>
                <p className="text-red-100">@maisyshare9x</p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 transform hover:scale-105">
                <div className="text-3xl mb-3">📍</div>
                <h3 className="font-bold mb-2">{t('common.address')}</h3>
                <p className="text-red-100">{t('about.contact.address')}</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="tel:0905759986"
                className="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-bold rounded-full hover:bg-white hover:text-red-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                <span className="mr-2">📞</span>
                0905759986
              </a>
              <a
                href="https://www.instagram.com/maisyshare9x/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-8 py-4 bg-white text-red-600 font-bold rounded-full hover:bg-red-50 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                <span className="mr-2">📷</span>
                Instagram
              </a>
            </div>

            <div className="mt-16 flex items-center justify-center">
              <div className="flex items-center text-red-200">
                <div className="w-8 h-px bg-red-300 mr-4"></div>
                <span className="text-sm font-medium">🍕 Pizza Mỹ - From Da Nang to Vietnam 🇻🇳</span>
                <div className="w-8 h-px bg-red-300 ml-4"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}