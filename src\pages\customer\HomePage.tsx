import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowRightIcon, StarIcon } from '@heroicons/react/24/solid';
import Button from '../../components/ui/Button';
import { AddressSection } from '../../components/home/<USER>';
import { ROUTES } from '../../utils/constants';

export default function HomePage() {
  const { t } = useTranslation();

  const featuredProducts = [
    {
      id: '1',
      name: t('products.margherita.name'),
      price: 299000,
      image: '/assets/pizza.avif',
      rating: 4.8,
      description: t('products.margherita.description'),
    },
    {
      id: '2',
      name: t('products.pepperoni.name'),
      price: 349000,
      image: '/assets/pizza.avif',
      rating: 4.9,
      description: t('products.pepperoni.description'),
    },
    {
      id: '3',
      name: t('products.supreme.name'),
      price: 399000,
      image: '/assets/pizza.avif',
      rating: 4.7,
      description: t('products.supreme.description'),
    },
  ];

  const categories = [
    {
      name: t('menu.categories.vegetarian'),
      image: '/images/category-vegetarian.jpg',
      href: `${ROUTES.MENU}?category=vegetarian`,
    },
    {
      name: t('menu.categories.meat'),
      image: '/images/category-meat.jpg',
      href: `${ROUTES.MENU}?category=meat`,
    },
    {
      name: t('menu.categories.combo'),
      image: '/images/category-combo.jpg',
      href: `${ROUTES.MENU}?category=combo`,
    },
  ];

  return (
    <>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('homepage.hero.title')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              {t('homepage.hero.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to={ROUTES.MENU}>
                <Button size="lg" className="bg-orange-500 hover:bg-orange-600 text-white font-semibold">
                  {t('homepage.hero.cta')}
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to={ROUTES.MENU}>
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-orange-600">
                  {t('homepage.hero.viewMenu')}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {t('homepage.features.title')}
            </h2>
            <p className="text-lg text-gray-600">
              {t('menu.subtitle')}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featuredProducts.map((product) => (
              <div
                key={product.id}
                className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
              >
                <div className="aspect-w-16 aspect-h-9">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-48 object-cover"
                    onError={(e) => {
                      e.currentTarget.src = `https://via.placeholder.com/400x200?text=${product.name}`;
                    }}
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-xl font-semibold text-gray-900">
                      {product.name}
                    </h3>
                    <div className="flex items-center">
                      <StarIcon className="h-4 w-4 text-yellow-400" />
                      <span className="ml-1 text-sm text-gray-600">
                        {product.rating}
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4">{product.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-primary-600">
                      ${product.price}
                    </span>
                    <Link to={`${ROUTES.MENU}/${product.id}`}>
                      <Button size="sm">
                        View Details
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Link to={ROUTES.MENU}>
              <Button variant="outline" size="lg">
                View All Menu
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Address Section */}
      {/* <AddressSection /> */}

      {/* CTA Section */}
      {/* <section className="bg-primary-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Order?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied customers who love our delicious pizzas. 
            Order now and get free delivery on orders over $25!
          </p>
          <Link to={ROUTES.MENU}>
            <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold">
              Start Ordering
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section> */}
    </>
  );
}
