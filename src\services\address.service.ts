import axios, { AxiosResponse } from 'axios';
import { API_CONFIG } from '../utils/constants';
import type { ApiResponse } from '../types';
import { authService } from './auth.service';

export interface Address {
  id?: string;
  label: string;
  fullAddress: string;
  latitude: number;
  longitude: number;
  defaultAddress?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateAddressRequest {
  label: string;
  fullAddress: string;
  latitude: number;
  longitude: number;
}

class AddressService {
  private baseURL = API_CONFIG.BASE_URL;

  // Create new address
  async createAddress(addressData: CreateAddressRequest): Promise<Address> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No access token found. Please login first.');
      }

      console.log('Creating address with data:', addressData);

      const response: AxiosResponse<ApiResponse<Address>> = await axios.post(
        `${this.baseURL}/addresses`,
        addressData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.code === 0 && response.data.result) {
        return response.data.result;
      }

      throw new Error(response.data.message || 'Failed to create address');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Failed to create address');
    }
  }

  // Get all addresses
  async getAddresses(): Promise<Address[]> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No access token found. Please login first.');
      }

      const response: AxiosResponse<ApiResponse<Address[]>> = await axios.get(
        `${this.baseURL}/addresses`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.data.code === 0 && response.data.result) {
        return response.data.result;
      }

      throw new Error(response.data.message || 'Failed to get addresses');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Failed to get addresses');
    }
  }

  // Update address
  async updateAddress(id: string, addressData: Partial<CreateAddressRequest>): Promise<Address> {
    try {
      const token = this.getAccessToken();
      if (!token) {
        throw new Error('No access token found. Please login first.');
      }

      const response: AxiosResponse<ApiResponse<Address>> = await axios.put(
        `${this.baseURL}/addresses/${id}`,
        addressData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.code === 0 && response.data.result) {
        return response.data.result;
      }

      throw new Error(response.data.message || 'Failed to update address');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Failed to update address');
    }
  }

  // Delete address
  async deleteAddress(id: string): Promise<void> {
    try {
      const token = this.getAccessToken();
      if (!token) {
        throw new Error('No access token found. Please login first.');
      }

      const response: AxiosResponse<ApiResponse<void>> = await axios.delete(
        `${this.baseURL}/addresses/${id}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.data.code !== 0) {
        throw new Error(response.data.message || 'Failed to delete address');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Failed to delete address');
    }
  }
}

export const addressService = new AddressService();
