import axios, { AxiosResponse } from 'axios';
import Cookies from 'js-cookie';
import { 
  User, 
  AuthTokens, 
  LoginCredentials, 
  RegisterData, 
  ApiResponse 
} from '../types';
import { API_CONFIG, API_ENDPOINTS, AUTH_CONFIG } from '../utils/constants';

class AuthService {
  private baseURL = API_CONFIG.BASE_URL;

  // Token management
  setTokens(tokens: AuthTokens): void {
    // Store access token in memory/state (more secure)
    // Store refresh token in httpOnly cookie (most secure) or localStorage
    Cookies.set(AUTH_CONFIG.TOKEN_KEY, tokens.token, {
      expires: 7, // 7 days - backend doesn't provide expiry time
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    Cookies.set(AUTH_CONFIG.REFRESH_TOKEN_KEY, tokens.refreshToken, {
      expires: 7, // 7 days
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });
  }

  getAccessToken(): string | null {
    return Cookies.get(AUTH_CONFIG.TOKEN_KEY) || null;
  }

  getRefreshToken(): string | null {
    return Cookies.get(AUTH_CONFIG.REFRESH_TOKEN_KEY) || null;
  }

  removeTokens(): void {
    Cookies.remove(AUTH_CONFIG.TOKEN_KEY);
    Cookies.remove(AUTH_CONFIG.REFRESH_TOKEN_KEY);
    localStorage.removeItem(AUTH_CONFIG.USER_KEY);
  }

  // User data management
  setUser(user: User): void {
    localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(user));
  }

  getUser(): User | null {
    const userData = localStorage.getItem(AUTH_CONFIG.USER_KEY);
    return userData ? JSON.parse(userData) : null;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    const token = this.getAccessToken();
    const user = this.getUser();
    return !!(token && user);
  }

  // Check if token is expired
  isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }

  // Refresh access token
  async refreshAccessToken(): Promise<AuthTokens | null> {
    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response: AxiosResponse<ApiResponse<AuthTokens>> = await axios.post(
        `${this.baseURL}${API_ENDPOINTS.AUTH.REFRESH}`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${refreshToken}`
          }
        }
      );

      if (response.data.code === 0 && response.data.result) {
        this.setTokens(response.data.result);
        return response.data.result;
      }

      throw new Error('Failed to refresh token');
    } catch (error) {
      this.removeTokens();
      return null;
    }
  }

  // Login
  async login(credentials: LoginCredentials): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      const response: AxiosResponse<ApiResponse<AuthTokens>> =
        await axios.post(`${this.baseURL}${API_ENDPOINTS.AUTH.LOGIN}`, credentials);

      if (response.data.code === 0 && response.data.result) {
        const tokens = response.data.result;
        this.setTokens(tokens);

        // Get user profile after successful login
        const user = await this.getCurrentUser();
        return { user, tokens };
      }

      throw new Error(response.data.message || 'Login failed');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Login failed');
    }
  }

  // Register
  async register(userData: RegisterData): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      const response: AxiosResponse<ApiResponse<User>> =
        await axios.post(`${this.baseURL}${API_ENDPOINTS.AUTH.REGISTER}`, userData);

      if (response.data.code === 0 && response.data.result) {
        const user = response.data.result;
        this.setUser(user);

        // After registration, login to get tokens
        const loginResult = await this.login({
          username: userData.username,
          password: userData.password
        });

        return loginResult;
      }

      throw new Error(response.data.message || 'Registration failed');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Registration failed');
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      const token = this.getAccessToken();
      if (token) {
        await axios.post(`${this.baseURL}${API_ENDPOINTS.AUTH.LOGOUT}`, {
          token
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      }
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout API call failed:', error);
    } finally {
      this.removeTokens();
    }
  }

  // Forgot password
  async forgotPassword(email: string): Promise<void> {
    try {
      const response: AxiosResponse<ApiResponse<void>> = await axios.post(
        `${this.baseURL}${API_ENDPOINTS.AUTH.FORGOT_PASSWORD}`,
        { email }
      );

      if (response.data.code !== 0) {
        throw new Error(response.data.message || 'Failed to send reset email');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Failed to send reset email');
    }
  }

  // Reset password
  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      const response: AxiosResponse<ApiResponse<void>> = await axios.post(
        `${this.baseURL}${API_ENDPOINTS.AUTH.RESET_PASSWORD}`,
        { token, password: newPassword }
      );

      if (response.data.code !== 0) {
        throw new Error(response.data.message || 'Failed to reset password');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Failed to reset password');
    }
  }

  // Verify email
  async verifyEmail(token: string): Promise<void> {
    try {
      const response: AxiosResponse<ApiResponse<void>> = await axios.post(
        `${this.baseURL}${API_ENDPOINTS.AUTH.VERIFY_EMAIL}`,
        { token }
      );

      if (response.data.code !== 0) {
        throw new Error(response.data.message || 'Failed to verify email');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Failed to verify email');
    }
  }

  // Get current user profile
  async getCurrentUser(): Promise<User> {
    try {
      const token = this.getAccessToken();
      const response: AxiosResponse<ApiResponse<User>> = await axios.get(
        `${this.baseURL}${API_ENDPOINTS.USERS.PROFILE}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.data.code === 0 && response.data.result) {
        this.setUser(response.data.result);
        return response.data.result;
      }

      throw new Error(response.data.message || 'Failed to get user profile');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Failed to get user profile');
    }
  }

  // Update user profile
  async updateProfile(userData: Partial<User>): Promise<User> {
    try {
      const token = this.getAccessToken();
      const user = this.getUser();
      if (!user?.id) {
        throw new Error('User ID not found');
      }

      // Prepare update data with username (required by API)
      const updateData = {
        username: user.username, // Always include username
        fullName: userData.fullName || user.fullName,
        email: userData.email || user.email,
        phoneNumber: userData.phoneNumber || user.phoneNumber,
      };

      console.log('Updating profile with data:', updateData);

      const response: AxiosResponse<ApiResponse<User>> = await axios.put(
        `${this.baseURL}${API_ENDPOINTS.USERS.UPDATE_PROFILE}/${user.id}`,
        updateData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.code === 0 && response.data.result) {
        this.setUser(response.data.result);
        return response.data.result;
      }

      throw new Error(response.data.message || 'Failed to update profile');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Failed to update profile');
    }
  }

  // Change password
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      const response: AxiosResponse<ApiResponse<void>> = await axios.post(
        `${this.baseURL}${API_ENDPOINTS.USERS.CHANGE_PASSWORD}`,
        { currentPassword, newPassword }
      );

      if (response.data.code !== 0) {
        throw new Error(response.data.message || 'Failed to change password');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || 'Failed to change password');
    }
  }
}

export const authService = new AuthService();
