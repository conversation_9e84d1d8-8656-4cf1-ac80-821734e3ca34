import axios, { AxiosResponse } from 'axios';
import { API_CONFIG } from '../utils/constants';
import type { ApiResponse } from '../types';

// Pizza interface
export interface Pizza {
  id: string;
  name: string;
  description: string;
  price: number;
  image?: string;
  category?: string;
  ingredients?: string[];
  size?: 'small' | 'medium' | 'large';
  isAvailable?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Pizza filters for search/filter functionality
export interface PizzaFilters {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  size?: string;
  isAvailable?: boolean;
  search?: string;
}

class PizzaService {
  private baseURL = API_CONFIG.BASE_URL;

  // Get all pizzas
  async getAllPizzas(filters?: PizzaFilters): Promise<Pizza[]> {
    try {
      let url = `${this.baseURL}/pizzas`;
      
      // Add query parameters if filters are provided
      if (filters) {
        const params = new URLSearchParams();
        
        if (filters.category) params.append('category', filters.category);
        if (filters.minPrice !== undefined) params.append('minPrice', filters.minPrice.toString());
        if (filters.maxPrice !== undefined) params.append('maxPrice', filters.maxPrice.toString());
        if (filters.size) params.append('size', filters.size);
        if (filters.isAvailable !== undefined) params.append('isAvailable', filters.isAvailable.toString());
        if (filters.search) params.append('search', filters.search);
        
        const queryString = params.toString();
        if (queryString) {
          url += `?${queryString}`;
        }
      }

      console.log('Fetching pizzas from:', url);

      const response: AxiosResponse<ApiResponse<Pizza[]>> = await axios.get(url);

      if (response.data.code === 0 && response.data.result) {
        console.log('Pizzas fetched successfully:', response.data.result);
        return response.data.result;
      }

      throw new Error(response.data.message || 'Failed to fetch pizzas');
    } catch (error: any) {
      console.error('Error fetching pizzas:', error);
      throw new Error(error.response?.data?.message || error.message || 'Failed to fetch pizzas');
    }
  }

  // Get pizza by ID
  async getPizzaById(id: string): Promise<Pizza> {
    try {
      const url = `${this.baseURL}/pizzas/${id}`;
      
      console.log('Fetching pizza by ID from:', url);

      const response: AxiosResponse<ApiResponse<Pizza>> = await axios.get(url);

      if (response.data.code === 0 && response.data.result) {
        console.log('Pizza fetched successfully:', response.data.result);
        return response.data.result;
      }

      throw new Error(response.data.message || 'Failed to fetch pizza');
    } catch (error: any) {
      console.error('Error fetching pizza by ID:', error);
      
      if (error.response?.status === 404) {
        throw new Error('Pizza not found');
      }
      
      throw new Error(error.response?.data?.message || error.message || 'Failed to fetch pizza');
    }
  }

  // Search pizzas by name
  async searchPizzas(query: string): Promise<Pizza[]> {
    try {
      return await this.getAllPizzas({ search: query });
    } catch (error: any) {
      console.error('Error searching pizzas:', error);
      throw new Error(error.message || 'Failed to search pizzas');
    }
  }

  // Get pizzas by category
  async getPizzasByCategory(category: string): Promise<Pizza[]> {
    try {
      return await this.getAllPizzas({ category });
    } catch (error: any) {
      console.error('Error fetching pizzas by category:', error);
      throw new Error(error.message || 'Failed to fetch pizzas by category');
    }
  }

  // Get available pizzas only
  async getAvailablePizzas(): Promise<Pizza[]> {
    try {
      return await this.getAllPizzas({ isAvailable: true });
    } catch (error: any) {
      console.error('Error fetching available pizzas:', error);
      throw new Error(error.message || 'Failed to fetch available pizzas');
    }
  }
}

// Export singleton instance
export const pizzaService = new PizzaService();
export default pizzaService;
